#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏打包脚本
使用PyInstaller将游戏打包成exe文件
"""

import os
import shutil
import subprocess
import sys

def clean_build_files():
    """清理之前的构建文件"""
    print("清理之前的构建文件...")
    
    # 删除构建目录
    dirs_to_remove = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")
    
    # 删除spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        os.remove(spec_file)
        print(f"已删除文件: {spec_file}")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")

    # 检查必要的文件夹是否存在
    required_dirs = ['image', 'levels']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"错误: 缺少必要的目录 {dir_name}")
            return False
        print(f"✓ 找到目录: {dir_name}")

    # 列出要包含的文件
    print("\n包含的资源文件:")
    for root, dirs, files in os.walk('image'):
        for file in files:
            print(f"  图片: {os.path.join(root, file)}")

    for root, dirs, files in os.walk('levels'):
        for file in files:
            print(f"  关卡: {os.path.join(root, file)}")

    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个exe文件
        '--windowed',                   # 不显示控制台窗口
        '--name=推箱子游戏',             # 设置exe文件名
        '--add-data=image;image',       # 包含image目录
        '--add-data=levels;levels',     # 包含levels目录
        '--distpath=release',           # 输出目录
        '--clean',                      # 清理临时文件
        'main.py'                       # 主程序文件
    ]
    
    try:
        # 执行PyInstaller命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("构建成功！")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_release_package():
    """创建发布包"""
    print("创建发布包...")
    
    # 创建发布目录
    release_dir = "release"
    if not os.path.exists(release_dir):
        os.makedirs(release_dir)
    
    # 复制必要文件到发布目录
    files_to_copy = [
        'README.md',
        '关卡设计指南.md'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, release_dir)
            print(f"已复制: {file_name}")
    
    print(f"发布包已创建在 {release_dir} 目录中")

def main():
    """主函数"""
    print("=" * 50)
    print("推箱子游戏打包工具")
    print("=" * 50)
    
    # 检查是否安装了PyInstaller
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到PyInstaller，请先安装:")
        print("pip install pyinstaller")
        sys.exit(1)
    
    # 清理构建文件
    clean_build_files()
    
    # 构建exe
    if build_exe():
        # 创建发布包
        create_release_package()
        print("\n" + "=" * 50)
        print("打包完成！")
        print("可执行文件位置: release/推箱子游戏.exe")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("打包失败！请检查错误信息")
        print("=" * 50)
        sys.exit(1)

if __name__ == "__main__":
    main()
