#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏主程序
作者：熊秋锦、廖云川、叶马可
"""

import pygame
import sys
from sokoban_game import SokobanGame

# 游戏配置
WINDOW_WIDTH = 1080
WINDOW_HEIGHT = 900
FPS = 30
TITLE = "推箱子游戏 - Sokoban"

def main():
    """游戏主函数"""

    try:
        # 初始化Pygame
        pygame.init()

        # 创建游戏窗口
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption(TITLE)

        # 创建时钟对象控制帧率
        clock = pygame.time.Clock()

        # 创建游戏实例
        try:
            game = SokobanGame(screen)
        except Exception as e:
            print(f"游戏初始化失败: {e}")
            pygame.quit()
            sys.exit(1)

        # 游戏主循环
        running = True

        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    # 处理键盘输入
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_r:
                        # R键重置当前关卡
                        try:
                            game.reset_level()
                        except Exception as e:
                            print(f"重置关卡失败: {e}")
                            running = False
                    elif event.key == pygame.K_n:
                        # N键下一关
                        try:
                            if game.next_level():
                                print(f"进入关卡 {game.current_level}")
                            else:
                                print("已经是最后一关")
                        except Exception as e:
                            print(f"切换到下一关失败: {e}")
                            running = False
                    elif event.key == pygame.K_p:
                        # P键上一关
                        try:
                            if game.previous_level():
                                print(f"返回关卡 {game.current_level}")
                            else:
                                print("已经是第一关")
                        except Exception as e:
                            print(f"切换到上一关失败: {e}")
                            running = False
                    elif event.key in [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT,
                                     pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d]:
                        # 只有方向键才传递给handle_input
                        try:
                            game.handle_input(event.key)
                        except Exception as e:
                            print(f"处理玩家输入失败: {e}")
                            running = False

            # 更新游戏状态
            try:
                game.update()
            except Exception as e:
                print(f"游戏状态更新失败: {e}")
                running = False

            # 渲染游戏画面
            try:
                game.render()
            except Exception as e:
                print(f"游戏渲染失败: {e}")
                running = False

            # 更新显示
            pygame.display.flip()

            # 控制帧率
            clock.tick(FPS)

    except Exception as e:
        print(f"游戏发生致命错误: {e}")

    finally:
        # 清理资源
        pygame.quit()

if __name__ == "__main__":
    main()
