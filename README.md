# 推箱子游戏 (Sokoban Game)

基于Python和Pygame开发的经典推箱子游戏

## 团队成员
- 熊秋锦：负责游戏主逻辑设计、地图数据结构实现及胜负判定模块开发
- 廖云川：负责Pygame窗口初始化、键盘事件处理、角色与地图渲染及界面信息显示
- 叶马可：负责关卡文件设计、移动合法性检测、步数记录功能及整体测试与调试

## 游戏特性
- 🎮 经典推箱子游戏玩法
- 🎨 精美的像素艺术素材
- 🏃 角色动作动画（支持四个方向）
- 📊 实时显示关卡、步数、进度信息
- 🎯 多个精心设计的关卡
- ⌨️ 支持键盘和WASD控制
- 🔄 关卡重置和切换功能
- 🖼️ 自定义素材支持
- 🛠️ **完整的错误日志系统 v2.0**
- 📋 **自动错误记录（无弹窗干扰）**
- 🔍 **实时错误监控和调试支持**
- 🌏 **完美支持中文错误信息**

## 安装要求
- Python 3.6+
- Pygame 2.0+

## 安装步骤
1. 确保已安装Python
2. 安装Pygame：
   ```bash
   pip install pygame
   ```

## 运行游戏

### 方法一：带错误日志启动（推荐）
双击运行 `启动游戏_带错误日志.bat` 或执行：
```bash
python main.py
```
这种方式包含完整的错误日志和报告功能。

### 方法二：一键启动
双击运行 `启动游戏.bat` 或执行：
```bash
python start_game.py
```
一键启动会自动检查环境、显示游戏说明并启动游戏。

## 游戏控制
- **方向键** 或 **WASD**：移动角色
- **R键**：重置当前关卡
- **N键**：下一关
- **P键**：上一关
- **F1键**：查看错误历史（调试用）
- **ESC键**：退出游戏

## 游戏规则
1. 控制角色（蓝色方块）移动
2. 推动箱子（橙色方块）到目标点（黄色方块）
3. 当所有箱子都推到目标点时，关卡完成
4. 箱子在目标点上时显示为绿色
5. 不能推动多个箱子或将箱子推到墙上

## 游戏元素
- 🧱 **墙壁**：不可通过的障碍物（使用wall.png素材）
- 🏃 **玩家**：可控制的角色，支持四方向动作（使用character.png素材）
- 📦 **箱子**：需要推动的物体（使用box.png素材）
- 🎯 **目标点**：箱子的目标位置（使用destination.png素材）
- ✅ **箱子在目标点**：正确放置的箱子（使用boxfinished.png素材）

## 文件结构
```
推箱子游戏/
├── main.py                      # 游戏主程序
├── sokoban_game.py              # 游戏核心类
├── error_logger.py              # 错误日志记录器 🆕
├── error_reporter.py            # 错误报告界面 🆕
├── test_game.py                 # 游戏测试脚本
├── test_error_system.py         # 错误系统测试脚本 🆕
├── simple_error_test.py         # 简单错误测试 🆕
├── sprite_test.py               # 素材测试脚本
├── start_game.py                # 一键启动脚本
├── 启动游戏.bat                  # Windows批处理启动文件
├── 启动游戏_带错误日志.bat        # 带错误日志的启动文件 🆕
├── 错误日志系统说明.md            # 错误系统说明文档 🆕
├── image/                       # 游戏素材文件夹
│   ├── wall.png                 # 墙壁素材 (32x32)
│   ├── box.png                  # 箱子素材 (32x32)
│   ├── boxfinished.png          # 完成状态箱子素材 (32x32)
│   ├── destination.png          # 目标点素材 (32x32)
│   └── character.png            # 角色动作表 (128x128, 4x4帧)
├── levels/                      # 关卡文件夹
│   ├── level_1.txt              # 关卡1-5
│   └── ...
├── logs/                        # 错误日志文件夹 🆕
│   ├── sokoban_error_YYYY-MM-DD.log   # 每日错误日志
│   └── sokoban_crash_YYYY-MM-DD.log   # 每日崩溃日志
└── README.md                    # 说明文档
```

## 测试游戏
运行测试脚本检查游戏是否正常工作：
```bash
python test_game.py
```

## 测试素材
运行素材测试脚本查看所有游戏素材：
```bash
python sprite_test.py
```
在测试窗口中按空格键切换角色方向，ESC键退出。

## 添加新关卡
1. 在`levels/`文件夹中创建新的关卡文件（如`level_6.txt`）
2. 使用以下字符设计关卡：
   - `#`：墙壁
   - ` `：空地
   - `$`：箱子
   - `.`：目标点
   - `@`：玩家起始位置
   - `*`：箱子在目标点上
   - `+`：玩家在目标点上

## 技术特点
- **模块化设计**：地图数据、角色控制、渲染显示分离
- **数据结构**：二维列表存储地图，字符标识不同元素
- **图形界面**：Pygame实现30FPS流畅交互
- **素材系统**：支持PNG素材，自动缩放和方向检测
- **动作系统**：角色根据移动方向显示不同动作帧
- **可扩展性**：支持外部文件加载多关卡和自定义素材

## 素材规格
- **基础素材**：32x32像素PNG格式
- **角色素材**：128x128像素，包含4x4动作帧网格
  - 第1行：向下动作帧
  - 第2行：向左动作帧
  - 第3行：向右动作帧
  - 第4行：向上动作帧
- **自动缩放**：所有素材自动缩放到40x40像素显示
- **备选方案**：如果素材加载失败，自动使用颜色块显示

## 故障排除

### 中文显示问题
游戏会自动检测系统中文字体：
- 优先使用微软雅黑 (microsoftyahei)
- 备选字体：黑体、宋体、楷体、仿宋
- 如果没有中文字体，会自动切换到英文界面

### 错误日志系统问题
如果错误日志系统出现问题：
1. 检查是否有写入权限创建`logs/`目录
2. 运行 `python test_error_system.py` 测试系统功能
3. 查看 `错误日志系统说明.md` 获取详细文档

### 其他问题
如果游戏无法运行，请检查：
1. Python版本是否为3.6+
2. Pygame是否正确安装
3. 关卡文件是否存在于`levels/`文件夹中
4. 查看 `logs/` 目录中的错误日志获取详细错误信息

### 测试字体支持
运行字体测试脚本：
```bash
python simple_font_test.py
```

### 测试按键功能
运行按键测试脚本：
```bash
python game_key_test.py
```
这个脚本会显示按键响应和游戏状态变化。

### 测试错误日志系统
运行错误系统测试：
```bash
python test_error_system.py        # 完整测试（包含GUI界面）
python simple_error_test.py        # 简单测试（仅命令行）
```

## 错误日志系统详细说明 🆕

### 自动错误记录
- 所有运行时错误都会自动记录到日志文件
- 包含详细的堆栈跟踪和系统信息
- 按日期自动分类日志文件

### 错误报告界面
当游戏发生错误时，会自动显示友好的错误报告界面：
- 显示错误详情和相关信息
- 可以滚动查看完整内容
- 支持复制错误信息和保存报告
- 显示最近的错误历史

### 崩溃报告
游戏崩溃时会生成详细的崩溃报告：
- 包含完整的系统信息
- 记录崩溃时的游戏状态
- 自动保存到专门的崩溃日志文件

### 日志文件管理
- 自动创建 `logs/` 目录
- 按日期分类：`sokoban_error_YYYY-MM-DD.log`
- 自动清理7天前的旧日志文件
- 支持手动查看和分析日志

### 开发者调试功能
- 按 **F1** 键查看最近错误历史
- 使用 `@log_exception` 装饰器自动记录函数异常
- 支持自定义错误上下文和额外信息

更多详细信息请参考 `错误日志系统说明.md`

## 许可证
本项目仅用于学习和教育目的。
