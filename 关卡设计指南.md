# 推箱子游戏 - 关卡设计指南

## 🎮 游戏配置

- **窗口尺寸**: 1080 x 900 像素
- **方块大小**: 40 x 40 像素
- **UI预留空间**: 60 像素（顶部30px + 底部30px）

## 📏 地图尺寸限制

### 最大允许尺寸
- **最大宽度**: 27 个方块 (1080 ÷ 40 = 27)
- **最大高度**: 21 个方块 ((900 - 60) ÷ 40 = 21)

### 建议尺寸
- **小型关卡**: 8-12 x 6-10 方块
- **中型关卡**: 13-18 x 11-15 方块  
- **大型关卡**: 19-27 x 16-21 方块

## 🔧 关卡元素符号

| 符号 | 含义 | 说明 |
|------|------|------|
| `#` | 墙壁 | 不可通过的障碍物 |
| `@` | 玩家 | 玩家起始位置 |
| `$` | 箱子 | 需要推动的箱子 |
| `.` | 目标点 | 箱子的目标位置 |
| `*` | 箱子在目标点上 | 箱子已在正确位置 |
| `+` | 玩家在目标点上 | 玩家站在目标点上 |
| ` ` | 空地 | 可以行走的空间 |

## ✅ 设计原则

1. **平衡性**: 箱子数量 = 目标点数量
2. **可解性**: 确保关卡有解，避免死锁
3. **边界**: 用墙壁 `#` 围成完整边界
4. **唯一玩家**: 每个关卡只能有一个玩家 `@`

## 🛠️ 工具使用

### 检查关卡尺寸
```bash
# 检查所有关卡
python check_level_size.py

# 检查指定关卡
python check_level_size.py levels/level_1.txt
```

### 游戏内验证
游戏会自动检查关卡尺寸，如果超出窗口范围会拒绝加载并显示警告信息。

## 📝 示例关卡

### 简单关卡 (8x6)
```
########
#  .   #
#  $   #
#  @   #
#      #
########
```

### 中等关卡 (12x8)
```
############
#          #
# ###  ### #
# #.$  $.# #
# #      # #
# #  @   # #
# ###  ### #
############
```

## ⚠️ 常见错误

1. **尺寸超出**: 地图太大导致无法完整显示
2. **箱子目标不匹配**: 箱子数量与目标点数量不一致
3. **缺少边界**: 没有用墙壁围成完整边界
4. **无解设计**: 创建了无法完成的关卡

## 🔍 调试技巧

1. 使用 `check_level_size.py` 验证尺寸
2. 手动测试关卡可解性
3. 检查控制台输出的警告信息
4. 逐步增加关卡复杂度
